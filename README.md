# OCR preprocessing (OpenCV) – seznam transformací

Skript: `scripts/ocr_preprocess.py`

Použité transformace (v pořadí zpracování):

1. <PERSON><PERSON><PERSON><PERSON> velikosti (resize_longer_side)
   - <PERSON><PERSON><PERSON> strana je škálována na nastavený limit (DEFAULT_MAX_SIZE), 0 = bez změny.
2. Detekce dokumentu a perspektivní korekce
   - Nalezení největšího konvexního čtyřúhelníku (kontury) a four‑point perspective warp pro "scan" efekt (ořez a narovnání stránky).
3. Automatická orientace o násobky 90°
   - Volí 0/90/180/270° podle metriky zarovnání vodorovných řádků textu.
4. Převod do odstínů šedi
   - Standardní konverze BGR → Gray.
5. Korekce nehomogenního osvětlení
   - Odečtení rozmazaného pozadí (velký Gaussian blur) a normalizace dynamického rozsahu.
6. CLAHE (Contrast Limited Adaptive Histogram Equalization)
   - Lokální zvýšení kontrastu (nastavitelný clip limit a velikost dlaždic).
7. Odšumění
   - fastNlMeansDenoising na šedotónovém obraze (nastavitelná síla).
8. Jemné dorovnání sklonu (deskew)
   - Odhad sklonu pomocí Houghových přímek (medián úhlů) a rotace o malé úhly.
9. Binarizace (výběr metody)
   - Adaptive Gaussian / Adaptive Mean / Otsu / Sauvola (parametrizovatelné okno, C, k, R).
10. Morfologické operace
    - Opening a/nebo Closing s nastavitelnou velikostí jádra.

Poznámky:
- Výchozí hodnoty lze upravit přímo v horní části skriptu v sekci „USER CONFIG“ (např. volba metody binarizace, zapnutí/vypnutí detekce dokumentu, deskew atd.).
- Volitelně lze zapnout ukládání debug výstupů jednotlivých kroků do složek `<název>_debug` v cílovém adresáři.

